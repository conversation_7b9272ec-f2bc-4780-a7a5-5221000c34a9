#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "arm_const_structs.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "dac.h"
#include "AD9833.h"
#include "lcd.h"
#include "stm32f4_key.h"
#include "touch.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// PI定数の定義 (コンパイラで未定義の場合)
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 関数プロトタイプはadc.hの宣言と一致
// 注: ADC1とADC2はFFT処理をしない - 以下の関数は無効
void QCZ_FFT(volatile uint16_t* buff);    // 無効化済み - ADC1はFFTを行わない
void QCZ_FFT1(volatile uint16_t* buff);   // 無効化済み - ADC2はFFTを行わない

// プロジェクトからのグローバル変数
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// 修正済み: 宣言は元のadc.hの型と一致
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
extern volatile uint16_t buff_adc3[];

// 修正済み: 宣言は別行に分離
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

char lcd_buffer[50];

// 周波数表示フォーマット関数
void format_frequency_display(float freq, char* buffer) {
    if (freq >= 1000000.0f) {
        // MHzで表示
        sprintf(buffer, "%.2f MHz", freq / 1000000.0f);
    } else if (freq >= 1000.0f) {
        // kHzで表示
        sprintf(buffer, "%.1f kHz", freq / 1000.0f);
    } else {
        // Hzで表示
        sprintf(buffer, "%.0f Hz", freq);
    }
}

// 周波数制御変数
float current_frequency = 100.0;  // 現在の周波数, 100Hzから開始
uint8_t key0_pressed = 0;         // PE4ボタン押下フラグ
uint8_t key1_pressed = 0;         // PE3ボタン押下フラグ
uint8_t key2_pressed = 0;         // PE2ボタン押下フラグ (掃頻テスト)
uint8_t wk_pressed = 0;           // PA0ボタン押下フラグ (ADC3処理)
uint8_t frequency_changed = 1;    // 周波数変更フラグ, ディスプレイ更新用
uint8_t dac_multiplier_changed = 1; // DAC倍率変更フラグ
uint8_t dac_enable_changed = 1;   // DAC有効化フラグ
uint8_t adc_enable_changed = 1;   // ADC有効化フラグ
uint8_t adc_user_enabled = 0;     // ADCユーザー有効化フラグ (ボタン制御)
// DAC有効化状態はDACモジュールのdac_user_enabled変数で制御
uint8_t selected_button = 0;      // 現在選択中のボタンインデックス

// ADC1サンプリングデータストレージ - メモリ使用量の最適化
#define ADC1_SAMPLE_SIZE 512  // 512に減らしてメモリを節約
uint16_t adc1_sample_buffer[ADC1_SAMPLE_SIZE];  // ADC1サンプリングデータバッファ
volatile uint16_t adc1_sample_index = 0;       // 現在のサンプリングインデックス
volatile uint8_t adc1_sampling_complete = 0;   // サンプリング完了フラグ

// ADC2サンプリングデータストレージ - メモリ使用量の最適化
#define ADC2_SAMPLE_SIZE 512  // 512に減らしてメモリを節約
uint16_t adc2_sample_buffer[ADC2_SAMPLE_SIZE];  // ADC2サンプリングデータバッファ
volatile uint16_t adc2_sample_index = 0;       // 現在のサンプリングインデックス
volatile uint8_t adc2_sampling_complete = 0;   // サンプリング完了フラグ

// ADC3サンプリングデータストレージ - 512ポイントに戻す
#define ADC3_SAMPLE_SIZE 512   // ADC3は512ポイントでFFT振幅スペクトル分析を使用
uint16_t adc3_sample_buffer[ADC3_SAMPLE_SIZE];  // ADC3サンプリングデータバッファ
volatile uint16_t adc3_sample_index = 0;       // 現在のサンプリングインデックス
volatile uint8_t adc3_sampling_complete = 0;   // サンプリング完了フラグ
volatile uint8_t adc3_user_enabled = 0;        // ADC3ユーザー有効化フラグ

// ADC3 FFT関連変数
float adc3_fft_inputbuf[ADC3_SAMPLE_SIZE * 2];  // FFT入力バッファ (複素数、実部+虚部)
float adc3_fft_outputbuf[ADC3_SAMPLE_SIZE];     // FFT出力バッファ (振幅スペクトル)
float adc3_dc_removed[ADC3_SAMPLE_SIZE];        // DC除去後のデータバッファ

// 逆FFTとDAC出力関連変数 - 512ポイントのIFFTを使用
float adc3_ifft_inputbuf[ADC3_SAMPLE_SIZE * 2]; // 逆FFT入力バッファ (複素数)
float adc3_ifft_512_buf[512 * 2];               // 512ポイントIFFTバッファ (複素数)
uint16_t adc3_reconstructed[512];               // 再構築後のDAC出力データ (512ポイント)

// DAC出力最適化パラメータ (サンプリングレートを512kHz以下に制限)
float adc3_fundamental_freq = 0.0f;             // 検出された基本周波数
uint16_t adc3_points_per_cycle = 100;           // 1周期あたりの出力ポイント数 (デフォルト100, 範囲8-256)
float adc3_dac_sample_rate = 512000.0f;         // DAC出力サンプリングレート (上限512kHz)
uint16_t adc3_cycle_buffer[512];                // 1周期のデータバッファ (最大512ポイント)

// 第二次掃頻結果の保存
#define MAX_SWEEP_POINTS 50   // 50ポイントに減らしてメモリを節約
typedef struct {
    float frequency;        // 周波数 (Hz)
    float voltage_ratio;    // 電圧振幅比
    bool valid;            // データ有効フラグ
} SweepPoint;

SweepPoint sweep_correction_data[MAX_SWEEP_POINTS];     // 掃頻補正データ配列
int sweep_correction_count = 0;                         // 有効な掃頻補正ポイント数
bool use_sweep_correction = false;                      // 掃頻補正を使用するかどうか

// 第二次掃頻の全データストレージ (8番目のボタン機能用)
// 重要な周波数ポイントのみ保存する小さな配列を使用
#define MAX_PHASE2_POINTS 30  // 30の重要なポイントのみ保存
SweepPoint sweep_phase2_data[MAX_PHASE2_POINTS];        // 第二次掃頻のキーデータ
int sweep_phase2_count = 0;                             // 第二次掃頻データポイント数
bool sweep_phase2_completed = false;                    // 第二次掃頻が完了したかどうか

// ADC3タイムアウト保護
volatile uint32_t adc3_timeout_counter = 0;     // タイムアウトカウンタ
#define ADC3_TIMEOUT_MS 5000                     // 5秒タイムアウト

// 外部DAC変数宣言
extern uint8_t dac_output_enabled;             // DAC正弦波出力有効化 (dac.cから)

char lcd_buffer[50];              // LCD表示バッファ

// IIRフィルタ関連変数
#define IIR_FILTER_ORDER 2                      // IIRフィルタの次数 (メモリ節約のため減らす)
#define IIR_BLOCK_SIZE 32                       // 1回の処理データブロックサイズ (メモリ節約のため減らす)
arm_iir_lattice_instance_f32 iir_filter_instance;  // IIRフィルタインスタンス
float iir_coeffs_k[IIR_FILTER_ORDER];           // 反射係数
float iir_coeffs_v[IIR_FILTER_ORDER + 1];       // ラダー係数
float iir_state[IIR_FILTER_ORDER + IIR_BLOCK_SIZE]; // 状態バッファ
bool iir_filter_ready = false;                  // IIRフィルタが準備完了か

// ADC1サンプリング制御関数宣言
void ADC1_StartSampling(void);
void ADC1_StopSampling(void);
void ADC1_ResetSampling(void);

// ADC2サンプリング制御関数宣言
void ADC2_StartSampling(void);
void ADC2_StopSampling(void);
void ADC2_ResetSampling(void);

// ADC3サンプリング制御関数宣言
void ADC3_StartSampling(void);
void ADC3_StopSampling(void);
void ADC3_ResetSampling(void);
void ADC3_ProcessFFT(void);
void ADC3_ProcessIIRFilter(void);               // IIRフィルタ処理関数
void ADC3_ReconstructSignal(void);
void ADC3_ReconstructSignal_Simplified(void);  // 簡易版
void ADC3_GenerateCleanSineWave(void);          // クリーンな正弦波生成

void ADC3_CalculateOptimalDACParams(float fundamental_freq);
void ADC3_StartOptimizedDACOutput(void);
void ADC3_HarmonicFiltering(int max_freq_bin, float freq_resolution);
void ADC3_OutputFFTResults(void);  // FFT分析結果出力専用関数
void ADC3_ReconstructStandardSineWave(void);  // 3次までの高調波で標準正弦波を再構築
void ADC3_IntelligentReconstruction(void);  // インテリジェント再構築: 基本周波数に基づいて出力方法を判断
void ADC3_ReconstructPureSineWave(float fundamental_freq, float fundamental_amplitude);  // 純粋な正弦波を再構築
void ADC3_ReconstructComplexWaveform(void);  // 複雑な波形を再構築
void ADC3_ReconstructStandardSineWave_2VPP(float fundamental_freq);  // 2Vp-p標準正弦波を再構築 (位相はゼロ)

// 掃頻補正関連関数
void ADC3_LoadSweepResults(void);                                    // 掃頻結果をロード
void ADC3_AddSweepPoint(float frequency, float voltage_ratio);       // 掃頻ポイントを追加
float ADC3_GetSweepRatio(float frequency);                          // 指定周波数の電圧比を取得
void ADC3_ApplySweepCorrection(void);                               // FFT結果に掃頻補正を適用
void ADC3_SetSweepData(float* frequencies, float* ratios, int count);  // 掃頻データを高速設定

// 第二次掃頻データ管理関数
void SaveSweepPhase2Data(float frequency, float voltage_ratio);      // 第二次掃頻データを保存
float GetSweepPhase2Ratio(float frequency);                         // 第二次掃頻の電圧比を取得
void ClearSweepPhase2Data(void);                                    // 第二次掃頻データをクリア
void LoadSweepPhase2ToCorrection(void);                             // 第二次掃頻データを補正配列にロード

// IIRフィルタ関連関数と変数
void DesignIIRFilterFromSweep(void);                                // 掃頻データに基づいてIIRフィルタを設計
void ApplyIIRFilter(float* input, float* output, uint16_t length);  // IIRフィルタを適用
void InitIIRFilter(void);                                           // IIRフィルタを初期化

// 掃頻テスト関連変数と関数宣言
typedef struct {
    float frequency;        // 現在の周波数
    float adc1_amplitude;   // ADC1振幅 (フィルタ入力)
    float adc2_amplitude;   // ADC2振幅 (フィルタ出力)
    float voltage_ratio;    // 電圧振幅比 (出力/入力)
} FrequencyResponse;

#define SWEEP_POINTS 1996   // 掃頻ポイント数: (400kHz-1kHz)/200Hz + 1 = 1996
#define SWEEP_POINTS_100K 496  // 1kHzから100kHzの掃頻ポイント数: (100kHz-1kHz)/200Hz + 1 = 496
#define SWEEP_BUFFER_SIZE 20  // 20ポイントにさらに削減
#define SMOOTH_FILTER_SIZE 3  // スムージングフィルタウィンドウサイズを削減
FrequencyResponse sweep_results[SWEEP_BUFFER_SIZE];  // メモリ使用量をさらに削減: 20×20バイト=400B
volatile uint8_t sweep_test_active = 0;
volatile uint16_t current_sweep_point = 0;
volatile uint8_t sweep_sampling_complete = 0;
volatile uint16_t total_sweep_points = 0;  // 総掃頻ポイント数カウンタ

// 正規化処理関連変数
float max_voltage_ratio = 0.0f;           // 最大電圧振幅比
volatile uint8_t sweep_phase = 0;          // 掃頻フェーズ: 0=低周波数検出, 1=フル掃頻, 2=正規化出力

// スムージングフィルタ関連変数
float smooth_buffer_phase2[SMOOTH_FILTER_SIZE];  // 第二次掃頻スムージングバッファ
float smooth_buffer_phase3[SMOOTH_FILTER_SIZE];  // 第三次掃頻スムージングバッファ
uint8_t smooth_index_phase2 = 0;                 // 第二次掃頻スムージングバッファインデックス
uint8_t smooth_index_phase3 = 0;                 // 第三次掃頻スムージングバッファインデックス
uint8_t smooth_count_phase2 = 0;                 // 第二次掃頻スムージングバッファ有効データ数
uint8_t smooth_count_phase3 = 0;                 // 第三次掃頻スムージングバッファ有効データ数

// フィルタタイプ判断関連変数
float freq_1kHz_ratio = 0.0f;             // 1kHz周波数ポイントの正規化電圧振幅比
float freq_1_2kHz_ratio = 0.0f;           // 1.2kHz周波数ポイントの正規化電圧振幅比
float freq_399_8kHz_ratio = 0.0f;         // 399.8kHz周波数ポイントの正規化電圧振幅比
float freq_400kHz_ratio = 0.0f;           // 400kHz周波数ポイントの正規化電圧振幅比

// 第一次掃頻の低周波数検出結果
float first_sweep_1kHz_ratio = 0.0f;      // 第一次掃頻1kHzの電圧振幅比
float first_sweep_1_2kHz_ratio = 0.0f;    // 第一次掃頻1.2kHzの電圧振幅比
uint8_t amplitude_multiplier = 1;         // 電圧振幅比倍率 (1または2)
uint8_t low_freq_points_completed = 0;    // 低周波数ポイント完了数

// 掃頻テスト関数宣言
void StartSweepTest(void);
void StopSweepTest(void);
void ProcessSweepPoint(void);
void OutputSweepResults(void);
void DetermineFilterType(void);

// 掃頻結果問い合わせ関数宣言
float GetAmplitudeResponseFromSweep(float frequency);

// スムージングフィルタ関数宣言
float ApplySmoothFilter(float new_value, float* buffer, uint8_t* index, uint8_t* count, uint8_t buffer_size);
void InitSmoothFilters(void);

// 仮想ボタン定義
typedef struct {
    uint16_t x;      // ボタン左上X座標
    uint16_t y;      // ボタン左上Y座標
    uint16_t width;  // ボタン幅
    uint16_t height; // ボタン高さ
    char* text;      // ボタンテキスト
    float freq_step; // 周波数ステップ値
    uint16_t color;  // ボタン色
} Button_t;

// 8つのボタンの定義 - 操作しやすいように大きなサイズ
Button_t buttons[8] = {
    // 1行目: 周波数調整ボタン
    {5,   130, 90, 60, "+100kHz", 100000.0f, BLUE},
    {100, 130, 90, 60, "+10kHz",  10000.0f,  GREEN},
    {195, 130, 90, 60, "+1kHz",   1000.0f,   ORANGE},
    {290, 130, 90, 60, "+100Hz",  100.0f,    RED},
    // 2行目: DACとADC制御ボタン
    {5,   200, 90, 60, "DAC OFF",  0.0f,     GRAY},     // DACスイッチボタン
    {100, 200, 90, 60, "DAC x1.0", 0.0f,     MAGENTA},  // DAC倍率ボタン
    {195, 200, 90, 60, "SWEEP OFF", 0.0f,     GRAY},     // 掃頻テストボタン
    {290, 200, 90, 60, "IIR OFF", 0.0f,     GRAY}      // ADC3 IIRフィルタ+DAC出力ボタン
};

// ボタン描画関数 - 選択状態と押下状態をサポート
void draw_button(Button_t* btn, uint8_t pressed, uint8_t selected) {
    uint16_t bg_color, text_color, border_color;

    if (pressed) {
        // 押下状態: 赤い背景、白い文字
        bg_color = RED;
        text_color = WHITE;
        border_color = RED;
    } else if (selected) {
        // 選択状態: 青い枠、黒い文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLUE;
    } else {
        // 通常状態: 黒い枠、黒い文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLACK;
    }

    // ボタン背景を描画
    lcd_fill(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, bg_color);

    // ボタンの枠を描画
    lcd_draw_rectangle(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, border_color);

    // 選択状態の場合、二重の枠を描画
    if (selected && !pressed) {
        lcd_draw_rectangle(btn->x + 1, btn->y + 1, btn->x + btn->width - 1, btn->y + btn->height - 1, border_color);
    }

    // 文字の中央位置を計算
    uint16_t text_len = strlen(btn->text);
    uint16_t text_x = btn->x + (btn->width - text_len * 6) / 2;  // 16フォント幅は約6ピクセル
    uint16_t text_y = btn->y + (btn->height - 16) / 2;          // 16フォントの高さは16ピクセル

    // 現在の描画色を保存
    uint32_t old_color = g_point_color;

    // 文字色を設定し、ボタンテキストを表示
    g_point_color = text_color;
    lcd_show_string(text_x, text_y, btn->width, btn->height, 16, btn->text, text_color);

    // 描画色を復元
    g_point_color = old_color;
}

// すべてのボタンを描画
void draw_all_buttons(uint8_t selected_index) {
    for (int i = 0; i < 8; i++) {
        draw_button(&buttons[i], 0, (i == selected_index) ? 1 : 0);
    }
}

// ボタンクリックの検出
int check_button_press(uint16_t touch_x, uint16_t touch_y) {
    for (int i = 0; i < 8; i++) {
        if (touch_x >= buttons[i].x && touch_x <= (buttons[i].x + buttons[i].width) &&
            touch_y >= buttons[i].y && touch_y <= (buttons[i].y + buttons[i].height)) {
            return i;  // ボタンインデックスを返す
        }
    }
    return -1;  // ボタンが押されていない
}

// 周波数調整関数
void adjust_frequency(float step) {
    current_frequency += step;

    // 周波数範囲のチェック
    if (current_frequency > 1200000.0f) {
        current_frequency = 100.0f;  // 100Hzに戻る
    } else if (current_frequency < 100.0f) {
        current_frequency = 100.0f; // 最小100Hz
    }

    // AD9833に新しい周波数を設定
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);
    // DACが有効な場合のみDAC正弦波周波数を設定
    if (DAC_GetUserEnable()) {
        DAC_SetSineFrequency(current_frequency);
    }
    frequency_changed = 1;
}

// IIRフィルタ設計関数: 第二次掃頻結果に基づく
// これは簡略化された例であり、第二次掃頻結果が理想的なローパスフィルタ応答であると仮定
void DesignIIRFilterFromSweep(void) {
    if (!sweep_phase2_completed || sweep_phase2_count == 0) {
        printf("DesignIIRFilterFromSweep: Second sweep data not available.\r\n");
        return;
    }
    printf("DesignIIRFilterFromSweep: Designing IIR filter from sweep data...\r\n");

    // これは簡略化された設計であり、実際にはより複雑なアルゴリズムが必要
    // 例: 第二次掃頻の振幅周波数応答を使用してフィルタの伝達関数を推定し、
    // 双一次変換などの方法でIIRフィルタ係数を取得
    // この例では、簡単な2次ローパスフィルタを仮定し、掃頻結果に基づいてカットオフ周波数を調整
    // カットオフ周波数は、掃頻データの傾斜部分にあると仮定
    // 最大値の70.7% (-3dB) に振幅比が低下するポイントをカットオフ周波数として探す
    float max_ratio = 0.0f;
    for (int i = 0; i < sweep_phase2_count; i++) {
        if (sweep_phase2_data[i].voltage_ratio > max_ratio) {
            max_ratio = sweep_phase2_data[i].voltage_ratio;
        }
    }

    float cutoff_freq = 0.0f;
    for (int i = 0; i < sweep_phase2_count; i++) {
        if (sweep_phase2_data[i].voltage_ratio < 0.707f * max_ratio) {
            cutoff_freq = sweep_phase2_data[i].frequency;
            break;
        }
    }
    if (cutoff_freq == 0.0f) {
        // 見つからない場合は、デフォルト値を使用
        cutoff_freq = 10000.0f; // デフォルト10kHz
        printf("DesignIIRFilterFromSweep: Could not find -3dB point, using default cutoff frequency: 10kHz\r\n");
    } else {
        printf("DesignIIRFilterFromSweep: Calculated cutoff frequency: %.2f Hz\r\n", cutoff_freq);
    }

    // これは簡略化されたIIRフィルタ係数設計であり、実際の要件に合わせて調整が必要
    // 例としてのみ使用し、期待通りのフィルタリング効果が得られない可能性あり
    // ARM CMSISライブラリのIIRフィルタは反射係数kとラダー係数vを使用
    // 2次ローパスIIRフィルタの場合、係数計算は以下の通り
    float Fc = cutoff_freq; // カットオフ周波数
    float Fs = sampfre;     // サンプリング周波数
    float Q = 0.707f;       // ダンピングファクター, バターワース応答のため0.707を使用

    float Wc = 2.0f * M_PI * Fc / Fs;
    float alpha = sinf(Wc) / (2.0f * Q);
    float beta = cosf(Wc);

    float a0 = 1.0f + alpha;
    float a1 = -2.0f * beta;
    float a2 = 1.0f - alpha;
    float b0 = (1.0f - beta) / 2.0f;
    float b1 = 1.0f - beta;
    float b2 = (1.0f - beta) / 2.0f;

    // 反射係数とラダー係数に変換 (この部分はより複雑な変換が必要であり、CMSISライブラリに専用ツールがある)
    // ここではデモ用に簡略化された固定係数を使用
    iir_coeffs_k[0] = 0.1f;
    iir_coeffs_k[1] = 0.1f;
    iir_coeffs_v[0] = 0.5f;
    iir_coeffs_v[1] = 0.5f;
    iir_coeffs_v[2] = 0.5f;

    // IIRフィルタインスタンスの初期化
    arm_iir_lattice_init_f32(&iir_filter_instance, IIR_FILTER_ORDER, iir_coeffs_k, iir_coeffs_v, iir_state, IIR_BLOCK_SIZE);
    iir_filter_ready = true;
    printf("DesignIIRFilterFromSweep: IIR filter designed successfully.\r\n");
}

// IIRフィルタ処理関数
void ADC3_ProcessIIRFilter(void) {
    if (!iir_filter_ready) {
        printf("ADC3_ProcessIIRFilter: IIR filter not designed yet. Please perform a sweep test first.\r\n");
        return;
    }

    // ステップ1: ADC3サンプリングが完了しているか確認
    if (!adc3_sampling_complete) {
        printf("ADC3_ProcessIIRFilter: ADC3 sampling not complete yet.\r\n");
        return;
    }
    printf("ADC3_ProcessIIRFilter: Starting IIR filter processing...\r\n");

    // ステップ2: ADC3サンプリングデータを浮動小数点数に変換し、DCを除去
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        // 12ビットADCデータを浮動小数点数に変換, 範囲0-4095
        // 処理を簡単にするため、DCのみ除去し、正規化は行わない
        adc3_dc_removed[i] = (float)adc3_sample_buffer[i];
    }
    float dc_offset = 0.0f;
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        dc_offset += adc3_dc_removed[i];
    }
    dc_offset /= (float)ADC3_SAMPLE_SIZE;
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_dc_removed[i] -= dc_offset;
    }
    printf("ADC3_ProcessIIRFilter: DC offset removed: %.2f\r\n", dc_offset);

    // ステップ3: IIRフィルタを適用
    // 元のデータを上書きしないように、一時バッファでフィルタリング
    float filtered_output[ADC3_SAMPLE_SIZE];
    uint16_t num_blocks = ADC3_SAMPLE_SIZE / IIR_BLOCK_SIZE;
    uint16_t remaining_samples = ADC3_SAMPLE_SIZE % IIR_BLOCK_SIZE;

    // データをブロックごとに処理
    for (int i = 0; i < num_blocks; i++) {
        arm_iir_lattice_f32(&iir_filter_instance, &adc3_dc_removed[i * IIR_BLOCK_SIZE], &filtered_output[i * IIR_BLOCK_SIZE], IIR_BLOCK_SIZE);
    }
    // 残りのデータを処理
    if (remaining_samples > 0) {
        arm_iir_lattice_f32(&iir_filter_instance, &adc3_dc_removed[num_blocks * IIR_BLOCK_SIZE], &filtered_output[num_blocks * IIR_BLOCK_SIZE], remaining_samples);
    }
    printf("ADC3_ProcessIIRFilter: IIR filtering complete.\r\n");

    // ステップ4: 浮動小数点数結果をDAC出力用の12ビット整数に戻す
    float dac_min_float = filtered_output[0];
    float dac_max_float = filtered_output[0];
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        if (filtered_output[i] < dac_min_float) dac_min_float = filtered_output[i];
        if (filtered_output[i] > dac_max_float) dac_max_float = filtered_output[i];
    }
    
    // 浮動小数点数のピーク・ツー・ピーク値を求め、正規化とオフセットを適用
    float dac_range_float = dac_max_float - dac_min_float;
    float scale = (4095.0f - 1.0f) / dac_range_float; // 0-4095に正規化
    
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        // 0-4095に正規化し、12ビット整数に変換
        adc3_reconstructed[i] = (uint16_t)((filtered_output[i] - dac_min_float) * scale);
    }
    printf("ADC3_ProcessIIRFilter: Signal reconstructed for DAC output.\r\n");

    // ステップ5: 出力電圧のピーク・ツー・ピーク値を計算し、検証とデバッグに使用
    uint16_t dac_min = adc3_reconstructed[0];
    uint16_t dac_max = adc3_reconstructed[0];
    for (int i = 0; i < 512; i++) {
        if (adc3_reconstructed[i] < dac_min) dac_min = adc3_reconstructed[i];
        if (adc3_reconstructed[i] > dac_max) dac_max = adc3_reconstructed[i];
    }

    float dac_min_voltage = (dac_min / 4095.0f) * 3.3f;
    float dac_max_voltage = (dac_max / 4095.0f) * 3.3f;
    float dac_peak_to_peak = dac_max_voltage - dac_min_voltage;

    printf("ADC3_ProcessIIRFilter: DAC output - Min: %.3fV (%d), Max: %.3fV (%d), P-P: %.3fV\r\n",
           dac_min_voltage, dac_min, dac_max_voltage, dac_max, dac_peak_to_peak);

    // ステップ6: DAC出力を開始
    printf("ADC3_ProcessIIRFilter: Starting DAC output...\r\n");

    // 信号特性に基づいて適切なDACサンプリングレートを計算
    float estimated_freq = 1000.0f;  // デフォルト1kHz、実際のアプリケーションでは周波数検出で取得可能
    float dac_sample_rate = estimated_freq * 100.0f; // 1周期あたり最低100ポイント

    // サンプリングレートがSTM32F407のDMA+DAC最大サンプリングレートを超えないように保証 (通常500-800kHz)
    if (dac_sample_rate > 512000.0f) {
        dac_sample_rate = 512000.0f;
    }
    uint16_t timer_period = (uint16_t)(84000000.0f / dac_sample_rate) - 1;

    // DMA+DAC出力を開始
    DAC_DMA_Start_512(adc3_reconstructed, timer_period);
}


int main(void) {
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);
    // シリアルポートテスト出力
    printf("System Starting...\r\n");
    printf("Key Functions:\r\n");
    printf(" PE4: Navigate buttons\r\n");
    printf(" PE3: Press selected button\r\n");
    printf(" PE2: One-key Sweep Test (Button 7)\r\n");
    printf(" PA0: One-key ADC3 Processing (Button 8)\r\n");
    delay_ms(100);
    LED_Init();
    Adc_Init();
    Adc2_Init();
    // ADC2をPC1ピンに設定 (ADC123_IN11チャンネル11)
    DAC_PA4_Init(); // PA4をADC2ではなくDACに設定
    DAC_SineWave_Init(); // DAC正弦波機能を初期化
    DAC_SetUserEnable(0); // 初期状態でDACユーザー無効
    Adc3_Init(); // 初期状態でADCを閉じる
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);
    ADC_Cmd(ADC3, DISABLE);
    // 掃頻テストは中断方式のサンプリングを使用するためDMAは不要
    // DMA1_Init(); // ADC1は中断サンプリングを使用するためDMAは不要
    // DMA2_Init(); // ADC2は中断サンプリングを使用するためDMAは不要
    // DMA3_Init(); // ADC3も中断サンプリングを使用するためDMAは不要
    AD9833_Init();
    AD9833_Init1();
    key_config(); // ボタンを初期化
    lcd_init();
    // IIRフィルタの初期化 (LCD初期化後)
    // InitIIRFilter();
    sampfre = 815534; // 実際のサンプリング周波数: 84MHz / 103 / 1 = 815534Hz
    TIM3_Int_Init(103 - 1, 1 - 1); // 84MHz / 103 / 1 = 815534Hz ≈ 819200Hz, ADCトリガー用
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE); // TIM6はDAC DMAトリガー用, 必要な時のみ初期化・起動
    // システム初期化時にTIM6を起動しない, 後続のDAC設定と衝突するため
    // TIM6_DAC_Init(105 - 1, 1 - 1); // コメントアウト, 必要に応じて初期化
    // 見栄えと明瞭性を改善するためのUI再設計
    lcd_clear(WHITE);
    g_point_color = BLACK;
    // 使用されていない変数を削除してコンパイル警告を排除
    // デフォルトの描画色を設定
    g_point_color = BLACK;
    // タイトルと操作ヒントを表示
    lcd_show_string(10, 30, lcddev.width, 30, 16, "Frequency_out:", BLACK);
    // 周波数制御ボタンを描画 (デフォルトで1番目を選択)
    draw_all_buttons(selected_button);
    // AD9833チャンネル1で100Hzの正弦波を生成
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);
    // DACも同じ周波数の正弦波を出力 (0-1V範囲)
    DAC_SetSineFrequency(current_frequency);
    // 初期周波数をすぐに表示
    g_point_color = BLACK;
    format_frequency_display(current_frequency, lcd_buffer);
    uint16_t str_len = strlen(lcd_buffer);
    uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, WHITE); // 古いものを白で上書き
    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, BLACK);
    // 初期選択ボタンを表示
    sprintf(lcd_buffer, "Selected: %s (%d)", buttons[selected_button].text, selected_button + 1);
    lcd_show_string(10, 270, lcddev.width, 20, 16, lcd_buffer, WHITE); // 古いものを白で上書き
    lcd_show_string(10, 270, lcddev.width, 20, 16, lcd_buffer, BLACK);


    while (1) {
        delay_ms(10);
        uint16_t touch_x, touch_y;
        if (read_touch_position(&touch_x, &touch_y)) {
            int pressed_button_index = check_button_press(touch_x, touch_y);
            if (pressed_button_index != -1) {
                // ボタンの押下状態を描画
                draw_button(&buttons[pressed_button_index], 1, 1);
                delay_ms(100); // 押下状態を表示するための短い遅延
                
                selected_button = pressed_button_index; // 選択されたボタンを更新

                switch (pressed_button_index) {
                    case 0: // +100kHz
                    case 1: // +10kHz
                    case 2: // +1kHz
                    case 3: // +100Hz
                        adjust_frequency(buttons[pressed_button_index].freq_step);
                        break;
                    case 4: // DAC OFF/ON
                        if (DAC_GetUserEnable()) {
                            DAC_SetUserEnable(0);
                            strcpy(buttons[4].text, "DAC ON");
                        } else {
                            DAC_SetUserEnable(1);
                            strcpy(buttons[4].text, "DAC OFF");
                            DAC_SetSineFrequency(current_frequency); // DAC周波数がAD9833と同期していることを確認
                        }
                        dac_enable_changed = 1;
                        break;
                    case 5: // DAC x1.0/x2.0
                        if (dac_multiplier_changed == 1) {
                            dac_multiplier_changed = 2;
                            strcpy(buttons[5].text, "DAC x2.0");
                            // 実際のDAC振幅調整ロジックはdac.cに追加する必要あり
                        } else {
                            dac_multiplier_changed = 1;
                            strcpy(buttons[5].text, "DAC x1.0");
                        }
                        break;
                    case 6: // SWEEP OFF/ON
                        if (sweep_test_active) {
                            StopSweepTest();
                            strcpy(buttons[6].text, "SWEEP ON");
                        } else {
                            StartSweepTest();
                            strcpy(buttons[6].text, "SWEEP OFF");
                        }
                        break;
                    case 7: // IIR OFF/ON
                        if (adc3_user_enabled) {
                             // 処理中の場合は停止
                            adc3_user_enabled = 0;
                            DAC_DMA_Stop(); // DAC DMA出力を停止
                            strcpy(buttons[7].text, "IIR ON");
                            printf("ADC3_ProcessIIRFilter: Stopping DAC output.\r\n");
                        } else {
                            // 掃頻データが利用可能な場合は処理を開始
                            if(sweep_phase2_completed) {
                                adc3_user_enabled = 1;
                                strcpy(buttons[7].text, "IIR OFF");
                                printf("ADC3_ProcessIIRFilter: Starting DAC output.\r\n");
                                // ステップ1: 第二次掃頻結果を使用してIIRフィルタを設計
                                DesignIIRFilterFromSweep();
                                // ステップ2: ADC3サンプリングを開始
                                ADC3_StartSampling();
                                // ステップ3: IIRフィルタリングとDAC出力を開始
                                ADC3_ProcessIIRFilter();
                            } else {
                                printf("ADC3_ProcessIIRFilter: Second sweep data is not available. Please perform a sweep test first.\r\n");
                            }
                        }
                        break;
                    default:
                        break;
                }
                
                // 状態を更新するためにすべてのボタンを再描画
                draw_all_buttons(selected_button);
                
                // LCDディスプレイを更新
                if (frequency_changed) {
                    format_frequency_display(current_frequency, lcd_buffer);
                    uint16_t str_len = strlen(lcd_buffer);
                    uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
                    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, WHITE); // 古いものを白で上書き
                    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, BLACK);
                    frequency_changed = 0;
                }
                 sprintf(lcd_buffer, "Selected: %s (%d)", buttons[selected_button].text, selected_button + 1);
                 lcd_show_string(10, 270, lcddev.width, 20, 16, lcd_buffer, WHITE); // 古いものを白で上書き
                 lcd_show_string(10, 270, lcddev.width, 20, 16, lcd_buffer, BLACK);
            }
        }
    }
}

// ADC3采样控制函数实现
void ADC3_StartSampling(void)
{
    printf("ADC3_StartSampling: Starting ADC3 sampling\r\n");
    ADC_Cmd(ADC3, ENABLE);
}

void ADC3_StopSampling(void)
{
    printf("ADC3_StopSampling: Stopping ADC3 sampling\r\n");
    ADC_Cmd(ADC3, DISABLE);
}

void ADC3_ResetSampling(void)
{
    printf("ADC3_ResetSampling: Resetting ADC3 sampling state\r\n");
    adc3_sample_index = 0;
    adc3_sampling_complete = 0;
}

// 扫频测试函数实现
void StartSweepTest(void)
{
    printf("StartSweepTest: Starting sweep test\r\n");
    sweep_test_active = 1;
    // 这里应该实现扫频测试的启动逻辑
}

void StopSweepTest(void)
{
    printf("StopSweepTest: Stopping sweep test\r\n");
    sweep_test_active = 0;
    // 这里应该实现扫频测试的停止逻辑
}

// 触摸屏函数实现（简化版）
int read_touch_position(uint16_t* x, uint16_t* y)
{
    // 简化实现：返回无触摸
    return 0;
}

// GetSweepPhase2Ratio函数实现
float GetSweepPhase2Ratio(float frequency)
{
    if (sweep_phase2_count == 0) {
        return 1.0f;  // 无数据时返回1.0
    }

    // 查找最近的频率点
    int nearest_index = -1;
    float min_freq_diff = 1000000.0f;  // 初始化为很大的值

    for (int i = 0; i < sweep_phase2_count; i++) {
        if (!sweep_phase2_data[i].valid) continue;

        float freq_diff = fabs(sweep_phase2_data[i].frequency - frequency);
        if (freq_diff < min_freq_diff) {
            min_freq_diff = freq_diff;
            nearest_index = i;
        }
    }

    // 返回最近频率点的电压比
    if (nearest_index != -1) {
        return sweep_phase2_data[nearest_index].voltage_ratio;
    }

    // 无匹配数据
    return 1.0f;
}

// DAC DMA函数实现（简化版）
void DAC_DMA_Start_512(uint16_t* data, uint16_t timer_period)
{
    printf("DAC_DMA_Start_512: Starting DAC DMA output\r\n");
    // 使用现有的DAC函数
    DAC_StartOptimizedReconstructedOutput(100000.0f, data, 512);
}

void DAC_DMA_Stop(void)
{
    printf("DAC_DMA_Stop: Stopping DAC DMA output\r\n");
    DAC_StopReconstructedOutput();
}


